plugins {
    id 'java'
    id 'io.quarkus'
    id "com.diffplug.spotless" version "7.0.0.BETA2"
    id "com.google.protobuf" version "0.8.19"
    id 'org.kordamp.gradle.jandex' version '2.0.0'
    id 'org.sonarqube' version '5.1.0.4882'
    id "jacoco"
    id "me.champeau.jmh" version "0.7.2"
}
repositories {
    mavenCentral()
    mavenLocal()
    maven {
        url = uri("https://maven.pkg.github.com/HydraXTrader/exchange-generic-aeron-sdk")
        credentials {
            username = project.findProperty("gpr.user") ?: System.getenv("GIT_USERNAME")
            password = project.findProperty("gpr.token") ?: System.getenv("TOKEN")
        }
    }
    maven {
        url = uri("https://maven.pkg.github.com/coralblocks/CoralPool")
        credentials {
            username = project.findProperty("gpr.user") ?: System.getenv("GIT_USERNAME")
            password = project.findProperty("gpr.token") ?: System.getenv("TOKEN")
        }
    }
}

spotless {
    java {
        targetExclude("build/**")
        googleJavaFormat('1.23.0').reflowLongStrings().formatJavadoc(false).reorderImports(false).groupArtifact('com.google.googlejavaformat:google-java-format')
    }
}
quarkus {
    quarkusBuildProperties.put("quarkus.grpc.codegen.proto-directory", "${project.projectDir}/ext/proto")
}
def grpcVersion = '1.44.1'
def protocVersion = '3.19.2'

jandex {
    version = '3.1.7'
}

tasks.named('quarkusDev') {
    dependsOn tasks.named('jandex')
}

quarkusDev {
    jvmArgs = [
            "--add-opens=java.base/sun.nio.ch=ALL-UNNAMED",
            "--add-opens=java.base/sun.nio.ch=ALL-UNNAMED",
            "--add-opens=java.base/java.lang=ALL-UNNAMED"
    ]
}

dependencies {
    implementation 'io.quarkiverse.loggingsentry:quarkus-logging-sentry:2.0.7'
    implementation 'io.quarkus:quarkus-smallrye-health'
    implementation 'io.quarkus:quarkus-container-image-docker'
    implementation enforcedPlatform("${quarkusPlatformGroupId}:${quarkusPlatformArtifactId}:${quarkusPlatformVersion}")
    implementation 'org.jboss.slf4j:slf4j-jboss-logmanager'
    implementation 'io.quarkus:quarkus-config-yaml'
    implementation 'io.quarkus:quarkus-arc'
    implementation 'io.smallrye.config:smallrye-config'
    implementation 'io.quarkus:quarkus-jackson:3.15.1'
    implementation 'io.quarkus:quarkus-cache'

    implementation 'io.quarkus:quarkus-kubernetes-client'

    implementation 'io.quarkus:quarkus-reactive-pg-client'
    implementation 'io.quarkus:quarkus-hibernate-reactive-panache'

    testImplementation 'io.quarkus:quarkus-junit5:3.14.3'
    testImplementation 'org.mockito:mockito-core:5.12.0'
    testImplementation 'org.mockito:mockito-junit-jupiter:5.13.0'

    implementation "io.quarkus:quarkus-rest"
    implementation("io.quarkus:quarkus-vertx")

    // Rest Jackson
    implementation("io.quarkus:quarkus-rest-jackson")

    // Lombok
    compileOnly 'org.projectlombok:lombok:1.18.34'
    annotationProcessor 'org.projectlombok:lombok:1.18.34'

    implementation 'org.mapstruct:mapstruct:1.5.5.Final'
    annotationProcessor 'org.mapstruct:mapstruct-processor:1.5.5.Final'
    testAnnotationProcessor 'org.mapstruct:mapstruct-processor:1.5.5.Final'
    // aeron
    implementation 'io.aeron:aeron-all:1.43.0'
    implementation('io.hydrax.aeron:exchange-generic-aeron-sdk:0.3.28-SNAPSHOT') {
        changing = true
    }

    implementation 'com.coralblocks:coralpool:1.4.1'

    // Protobuf
    implementation 'io.grpc:grpc-core:1.66.0'
    implementation 'io.grpc:grpc-stub:1.66.0'
    implementation 'io.grpc:grpc-protobuf:1.66.0'
    implementation 'com.google.protobuf:protobuf-java:3.24.4'
    implementation 'javax.annotation:javax.annotation-api:1.3.2'
    implementation 'com.google.protobuf:protobuf-java-util:3.24.4'

    implementation 'com.google.guava:guava:33.3.0-jre'
    implementation 'org.apache.commons:commons-lang3:3.17.0'
    implementation group: 'net.openhft', name: 'jlbh', version: '1.25ea7'

    implementation 'org.openjdk.jmh:jmh-core:1.37'
    annotationProcessor 'org.openjdk.jmh:jmh-generator-annprocess:1.37'

    implementation 'io.quarkus:quarkus-scheduler'
    implementation group: 'it.unimi.dsi', name: 'fastutil', version: '8.5.15'
    implementation 'org.roaringbitmap:RoaringBitmap:1.3.0'
    implementation 'com.github.chrisvest:stormpot:4.1'
}

group 'io.hydrax.pricestreaming'
version '1.0.0-SNAPSHOT'

java {
    sourceCompatibility = JavaVersion.VERSION_21
    targetCompatibility = JavaVersion.VERSION_21
}

protobuf {
    protoc {
        artifact = "com.google.protobuf:protoc:${protocVersion}"
    }
    plugins {
        grpc {
            artifact = "io.grpc:protoc-gen-grpc-java:${grpcVersion}"
        }
    }
    generateProtoTasks {
        all()*.plugins {
            grpc {}
        }
    }
}

test {
    systemProperty "java.util.logging.manager", "org.jboss.logmanager.LogManager"
}
compileJava {
    options.encoding = 'UTF-8'
    options.compilerArgs << '-parameters'
}

tasks.build {
    dependsOn 'spotlessApply'
}

compileTestJava {
    options.encoding = 'UTF-8'
}

sourceSets {
    main {
        java {
            srcDirs 'build/generated/source/proto/main/grpc'
            srcDirs 'build/generated/source/proto/main/java'
        }
    }
}

tasks.register('getImageTag') {
    println rootProject.name + ":" + (version as CharSequence)
}

tasks.named('quarkusDependenciesBuild') {
    mustRunAfter(tasks.named('jandex'))
}

configurations.configureEach {
    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
}

sonar {
    properties {
        property("sonar.projectKey", "exchange-generic-order-routing-engine")
        property("sonar.host.url", "http://localhost:9000")
        property "sonar.login", "sqa_c992c91eb72d8e910614c18c44d0bd3edf300b1e"
        property "sonar.exclusions", "**/*Generated.java, **/*Entity.java,**/*Dto.java,**/*Controller.java"
    }
}

jacocoTestReport {
    reports {
        xml.required = true
    }
}

plugins.withType(JacocoPlugin) {
    tasks["test"].finalizedBy 'jacocoTestReport'
}
java {
    toolchain {
        languageVersion.set(JavaLanguageVersion.of(21))
    }
}

// JMH Configuration
jmh {
    jmhVersion = '1.37'
    includeTests = false
    duplicateClassesStrategy = DuplicatesStrategy.WARN
    jvmArgs = ['-Xms1G', '-Xmx1G']

    // JMH specific configurations
    fork = 1
    warmupIterations = 2
    iterations = 3
    timeUnit = 'ns'
    benchmarkMode = ['avgt']

    // Include all benchmarks (remove filter to run all)
    includes = ['.*Protobuf.*', '.*OrderEvent.*']

    // Output configuration
    humanOutputFile = project.file("${project.buildDir}/reports/jmh/human.txt")
    resultsFile = project.file("${project.buildDir}/reports/jmh/results.json")
    resultFormat = 'JSON'

    // Fix for "Archive contains more than 65535 entries" error
    zip64 = true

    // Ensure JMH uses the same Java version as the project
    javaLauncher = javaToolchains.launcherFor {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

// Fix JMH dependency issues
tasks.named('compileJmhJava') {
    dependsOn tasks.named('jandex')
}

tasks.named('jmhCompileGeneratedClasses') {
    dependsOn tasks.named('jandex')
}

// Fix JMH JAR task for large archives
tasks.named('jmhJar') {
    zip64 = true
    duplicatesStrategy = DuplicatesStrategy.WARN
}
