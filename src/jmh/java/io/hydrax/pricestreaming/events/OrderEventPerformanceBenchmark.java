package io.hydrax.pricestreaming.events;

import io.hydrax.aeron.client.ClientManager;
import io.hydrax.pricestreaming.cache.MarketCache;
import io.hydrax.pricestreaming.cache.OrderCache;
import io.hydrax.pricestreaming.cache.TradingVenueCache;
import io.hydrax.pricestreaming.domain.Order;
import io.hydrax.pricestreaming.exception.RejectionException;
import io.hydrax.pricestreaming.router.RoutingEngine;
import io.hydrax.pricestreaming.service.OrderService;
import io.hydrax.pricestreaming.service.TradingVenueAccountService;
import io.hydrax.pricestreaming.utils.UDec128Util;
import io.hydrax.proto.metwo.match.*;
import io.vertx.core.eventbus.Message;
import org.openjdk.jmh.annotations.*;
import org.openjdk.jmh.infra.Blackhole;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Specialized JMH Benchmark for OrderEvent performance optimization scenarios
 *
 * This benchmark focuses on:
 * - Cache hit/miss performance
 * - Order validation performance
 * - Routing engine performance
 * - Error handling performance
 * - Memory allocation patterns
 *
 * Run with:
 * ./gradlew jmh --include=".*OrderEventPerformanceBenchmark.*"
 */
@BenchmarkMode({Mode.AverageTime, Mode.Throughput})
@OutputTimeUnit(TimeUnit.NANOSECONDS)
@State(Scope.Benchmark)
@Fork(value = 1, jvmArgs = {"-Xms2G", "-Xmx2G", "-XX:+UseG1GC", "-XX:+UnlockExperimentalVMOptions"})
@Warmup(iterations = 5, time = 3, timeUnit = TimeUnit.SECONDS)
@Measurement(iterations = 10, time = 5, timeUnit = TimeUnit.SECONDS)
public class OrderEventPerformanceBenchmark {

    private OrderEvent orderEvent;
    private OrderService orderService;
    private ClientManager clientManager;
    
    // Performance test scenarios
    private Message<Order> fastPathOrder;
    private Message<Order> slowPathOrder;
    private Message<Order> rejectedOrder;
    private Message<Order> cacheHitOrder;
    private Message<Order> cacheMissOrder;
    
    // Counters for realistic test data
    private AtomicLong orderIdCounter = new AtomicLong(1);
    private AtomicLong sequenceCounter = new AtomicLong(1);

    @Setup(Level.Trial)
    public void setupTrial() {
        setupMocks();
        createPerformanceTestData();
    }

    private void setupMocks() {
        orderService = mock(OrderService.class);
        clientManager = mock(ClientManager.class);
        orderEvent = new OrderEvent(orderService, clientManager);

        // Setup fast path - successful order processing
        doNothing().when(orderService).placeOrder(any(Order.class));
        doNothing().when(orderService).cancelOrder(any(PsOrder.class));
        
        // Setup slow path - order processing with delays
        doAnswer(invocation -> {
            Thread.sleep(1); // Simulate slow processing
            return null;
        }).when(orderService).placeOrder(argThat(order -> 
            order.getPsOrder().getClOrdId().contains("SLOW")));
            
        // Setup rejection scenario
        doThrow(new RejectionException("Order rejected"))
            .when(orderService).placeOrder(argThat(order -> 
                order.getPsOrder().getClOrdId().contains("REJECT")));
    }

    private void createPerformanceTestData() {
        // Fast path order - minimal processing
        PsOrder fastOrder = createOptimizedPsOrder("FAST_ORDER")
            .setRequestType(RequestType.REQUEST_TYPE_NEW_ORDER)
            .setOrdType(PsOrderType.PS_ORDER_TYPE_MARKET)
            .setTimeInForce(TimeInForce.TIME_IN_FORCE_IMMEDIATE_OR_CANCEL)
            .build();
        fastPathOrder = createMockMessage(fastOrder);

        // Slow path order - complex processing
        PsOrder slowOrder = createOptimizedPsOrder("SLOW_ORDER")
            .setRequestType(RequestType.REQUEST_TYPE_NEW_ORDER)
            .setOrdType(PsOrderType.PS_ORDER_TYPE_LIMIT)
            .setTimeInForce(TimeInForce.TIME_IN_FORCE_GOOD_TILL_CANCEL)
            .setPremium(UDec128Util.from(BigDecimal.valueOf(0.1)))
            .setExpireTime(System.currentTimeMillis() + 86400000)
            .build();
        slowPathOrder = createMockMessage(slowOrder);

        // Rejected order
        PsOrder rejectedOrderPs = createOptimizedPsOrder("REJECT_ORDER")
            .setRequestType(RequestType.REQUEST_TYPE_NEW_ORDER)
            .build();
        rejectedOrder = createMockMessage(rejectedOrderPs);

        // Cache hit scenario - reuse same order structure
        PsOrder cacheHitOrderPs = createOptimizedPsOrder("CACHE_HIT")
            .setRequestType(RequestType.REQUEST_TYPE_NEW_ORDER)
            .setSymbol("BTCUSDT") // Common symbol
            .setMarketCode("SPOT") // Common market
            .build();
        cacheHitOrder = createMockMessage(cacheHitOrderPs);

        // Cache miss scenario - unique order structure
        PsOrder cacheMissOrderPs = createOptimizedPsOrder("CACHE_MISS")
            .setRequestType(RequestType.REQUEST_TYPE_NEW_ORDER)
            .setSymbol("RARE_SYMBOL_" + System.nanoTime())
            .setMarketCode("FUTURES_" + System.nanoTime())
            .build();
        cacheMissOrder = createMockMessage(cacheMissOrderPs);
    }

    private PsOrder.Builder createOptimizedPsOrder(String prefix) {
        long id = orderIdCounter.getAndIncrement();
        return PsOrder.newBuilder()
            .setClOrdId(prefix + "_" + id)
            .setSymbol("BTCUSDT")
            .setSide(Side.SIDE_BUY)
            .setOrdType(PsOrderType.PS_ORDER_TYPE_LIMIT)
            .setQty(UDec128Util.from(BigDecimal.valueOf(100.0)))
            .setPrice(UDec128Util.from(BigDecimal.valueOf(50000.0)))
            .setServiceAccountId("SERVICE_" + id)
            .setHoldingAccountId("HOLDING_" + id)
            .setMarketCode("SPOT")
            .setTimeInForce(TimeInForce.TIME_IN_FORCE_DAY)
            .setUserId("USER_" + id)
            .setTraceId("TRACE_" + id)
            .setRequestReceivedTime(System.currentTimeMillis())
            .setBaseBalanceAccountId("BASE_" + id)
            .setQuoteBalanceAccountId("QUOTE_" + id);
    }

    @SuppressWarnings("unchecked")
    private Message<Order> createMockMessage(PsOrder psOrder) {
        Message<Order> message = mock(Message.class);
        Order order = Order.builder()
            .psOrder(psOrder)
            .startSequence(sequenceCounter.getAndIncrement())
            .build();
        when(message.body()).thenReturn(order);
        return message;
    }

    // ========== Performance Path Benchmarks ==========

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void onOrderFastPath(Blackhole bh) {
        try {
            orderEvent.onOrder(fastPathOrder);
        } catch (Exception e) {
            bh.consume(e);
        }
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void onOrderSlowPath(Blackhole bh) {
        try {
            orderEvent.onOrder(slowPathOrder);
        } catch (Exception e) {
            bh.consume(e);
        }
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void onOrderRejectedPath(Blackhole bh) {
        try {
            orderEvent.onOrder(rejectedOrder);
        } catch (Exception e) {
            bh.consume(e); // Expected exception
        }
    }

    // ========== Cache Performance Benchmarks ==========

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void onOrderCacheHit(Blackhole bh) {
        try {
            orderEvent.onOrder(cacheHitOrder);
        } catch (Exception e) {
            bh.consume(e);
        }
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void onOrderCacheMiss(Blackhole bh) {
        try {
            orderEvent.onOrder(cacheMissOrder);
        } catch (Exception e) {
            bh.consume(e);
        }
    }

    // ========== Throughput Optimization Benchmarks ==========

    @Benchmark
    @BenchmarkMode(Mode.Throughput)
    @OperationsPerInvocation(1000)
    public void onOrderOptimizedThroughput(Blackhole bh) {
        // Optimized for maximum throughput
        for (int i = 0; i < 1000; i++) {
            try {
                orderEvent.onOrder(fastPathOrder);
            } catch (Exception e) {
                bh.consume(e);
            }
        }
    }

    @Benchmark
    @BenchmarkMode(Mode.Throughput)
    @OperationsPerInvocation(500)
    public void onOrderRealisticMix(Blackhole bh) {
        // Realistic mix of fast/slow/rejected orders
        for (int i = 0; i < 500; i++) {
            try {
                Message<Order> message;
                int scenario = i % 10;
                if (scenario < 7) {
                    message = fastPathOrder; // 70% fast path
                } else if (scenario < 9) {
                    message = slowPathOrder; // 20% slow path
                } else {
                    message = rejectedOrder; // 10% rejected
                }
                orderEvent.onOrder(message);
            } catch (Exception e) {
                bh.consume(e);
            }
        }
    }

    // ========== Memory Allocation Benchmarks ==========

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void onOrderMinimalAllocation(Blackhole bh) {
        // Test with pre-allocated, reused objects
        try {
            orderEvent.onOrder(fastPathOrder);
        } catch (Exception e) {
            bh.consume(e);
        }
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void onOrderHighAllocation(Blackhole bh) {
        // Test with fresh object creation each time
        PsOrder psOrder = createOptimizedPsOrder("HIGH_ALLOC")
            .setRequestType(RequestType.REQUEST_TYPE_NEW_ORDER)
            .setClOrdId("FRESH_" + System.nanoTime())
            .build();
        Message<Order> message = createMockMessage(psOrder);
        
        try {
            orderEvent.onOrder(message);
        } catch (Exception e) {
            bh.consume(e);
        }
    }

    // ========== Stress Test Benchmarks ==========

    @Benchmark
    @BenchmarkMode(Mode.Throughput)
    @Threads(1)
    @OperationsPerInvocation(10000)
    public void onOrderStressSingleThread(Blackhole bh) {
        for (int i = 0; i < 10000; i++) {
            try {
                orderEvent.onOrder(fastPathOrder);
            } catch (Exception e) {
                bh.consume(e);
            }
        }
    }

    @Benchmark
    @BenchmarkMode(Mode.Throughput)
    @Threads(4)
    @OperationsPerInvocation(2500)
    public void onOrderStressMultiThread(Blackhole bh) {
        for (int i = 0; i < 2500; i++) {
            try {
                orderEvent.onOrder(fastPathOrder);
            } catch (Exception e) {
                bh.consume(e);
            }
        }
    }
}
