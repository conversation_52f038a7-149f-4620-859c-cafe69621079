package io.hydrax.pricestreaming.events;

import io.hydrax.aeron.client.ClientManager;
import io.hydrax.pricestreaming.cache.MarketCache;
import io.hydrax.pricestreaming.cache.OrderCache;
import io.hydrax.pricestreaming.cache.TradingVenueCache;
import io.hydrax.pricestreaming.domain.Order;
import io.hydrax.pricestreaming.router.RoutingEngine;
import io.hydrax.pricestreaming.service.OrderService;
import io.hydrax.pricestreaming.service.TradingVenueAccountService;
import io.hydrax.pricestreaming.utils.UDec128Util;
import io.hydrax.proto.metwo.match.*;
import io.vertx.core.eventbus.Message;
import org.openjdk.jmh.annotations.*;
import org.openjdk.jmh.infra.Blackhole;
import org.mockito.Mockito;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Comprehensive JMH Benchmark for OrderEvent.onOrder method
 *
 * This benchmark tests various scenarios:
 * - Different order request types (NEW, CANCEL, EDIT, UNKNOWN)
 * - Different order complexities (simple vs complex orders)
 * - Performance under different load conditions
 * - Cache hit/miss scenarios
 *
 * Run with:
 * ./gradlew jmh --include=".*OrderEventBenchmark.*"
 *
 * For specific benchmarks:
 * ./gradlew jmh --include=".*OrderEventBenchmark.onOrderNewOrder.*"
 */
@BenchmarkMode({Mode.AverageTime, Mode.Throughput})
@OutputTimeUnit(TimeUnit.NANOSECONDS)
@State(Scope.Benchmark)
@Fork(value = 1, jvmArgs = {"-Xms2G", "-Xmx2G", "-XX:+UseG1GC"})
@Warmup(iterations = 3, time = 2, timeUnit = TimeUnit.SECONDS)
@Measurement(iterations = 5, time = 3, timeUnit = TimeUnit.SECONDS)
public class OrderEventBenchmark {

    // Test subjects
    private OrderEvent orderEvent;
    
    // Mock dependencies
    private OrderService orderService;
    private ClientManager clientManager;
    private OrderCache orderCache;
    private TradingVenueCache tradingVenueCache;
    private RoutingEngine routingEngine;
    private MarketCache marketCache;
    private TradingVenueAccountService tradingVenueAccountService;

    // Test data - different order types
    private Message<Order> newOrderMessage;
    private Message<Order> cancelOrderMessage;
    private Message<Order> editOrderMessage;
    private Message<Order> unknownOrderMessage;
    
    // Test data - different complexities
    private Message<Order> simpleOrderMessage;
    private Message<Order> complexOrderMessage;
    private Message<Order> largeQuantityOrderMessage;

    // Test data - different symbols and markets
    private Message<Order> btcOrderMessage;
    private Message<Order> ethOrderMessage;
    private Message<Order> futuresOrderMessage;
    private Message<Order> spotOrderMessage;

    // Test data - different order sides and types
    private Message<Order> buyLimitOrderMessage;
    private Message<Order> sellLimitOrderMessage;
    private Message<Order> buyMarketOrderMessage;
    private Message<Order> sellMarketOrderMessage;
    private Message<Order> stopLossOrderMessage;

    // Test data - edge cases
    private Message<Order> minQuantityOrderMessage;
    private Message<Order> maxQuantityOrderMessage;
    private Message<Order> expiredOrderMessage;
    private Message<Order> invalidOrderMessage;

    @Setup(Level.Trial)
    public void setupTrial() {
        // Initialize mocks
        orderService = mock(OrderService.class);
        clientManager = mock(ClientManager.class);
        orderCache = mock(OrderCache.class);
        tradingVenueCache = mock(TradingVenueCache.class);
        routingEngine = mock(RoutingEngine.class);
        marketCache = mock(MarketCache.class);
        tradingVenueAccountService = mock(TradingVenueAccountService.class);

        // Create OrderEvent instance
        orderEvent = new OrderEvent(orderService, clientManager);

        // Setup mock behaviors
        setupMockBehaviors();
        
        // Create test data
        createTestData();
    }

    private void setupMockBehaviors() {
        // Setup OrderService mock behaviors
        doNothing().when(orderService).placeOrder(any(Order.class));
        doNothing().when(orderService).cancelOrder(any(PsOrder.class));
        
        // Setup cache mock behaviors
        when(tradingVenueCache.selectCodeByTimeInForceAndOrderType(anyString(), anyString(), anyString()))
            .thenReturn(Arrays.asList("BINANCE", "COINBASE", "KRAKEN"));
        
        when(marketCache.getMarketModelBySymbolCode(anyString()))
            .thenReturn("standard");
            
        // Setup successful routing
        doNothing().when(routingEngine).route(any(Order.class), anyList());
    }

    private void createTestData() {
        // Create base UDec128 values
        UDec128 quantity = UDec128Util.from(BigDecimal.valueOf(100.0));
        UDec128 price = UDec128Util.from(BigDecimal.valueOf(50000.0));
        UDec128 largeQuantity = UDec128Util.from(BigDecimal.valueOf(10000.0));
        UDec128 premium = UDec128Util.from(BigDecimal.valueOf(0.1));

        // Create NEW ORDER message
        PsOrder newOrder = createBasePsOrder()
            .setRequestType(RequestType.REQUEST_TYPE_NEW_ORDER)
            .setQty(quantity)
            .setPrice(price)
            .build();
        newOrderMessage = createMockMessage(newOrder);

        // Create CANCEL ORDER message
        PsOrder cancelOrder = createBasePsOrder()
            .setRequestType(RequestType.REQUEST_TYPE_CANCEL_ORDER)
            .setOrderId("ORDER_123")
            .setOrigClOrdId("ORIG_123")
            .build();
        cancelOrderMessage = createMockMessage(cancelOrder);

        // Create EDIT ORDER message (currently not implemented)
        PsOrder editOrder = createBasePsOrder()
            .setRequestType(RequestType.REQUEST_TYPE_EDIT_ORDER)
            .setQty(quantity)
            .setPrice(price)
            .build();
        editOrderMessage = createMockMessage(editOrder);

        // Create UNKNOWN ORDER message
        PsOrder unknownOrder = createBasePsOrder()
            .setRequestType(RequestType.UNRECOGNIZED)
            .build();
        unknownOrderMessage = createMockMessage(unknownOrder);

        // Create SIMPLE ORDER message
        PsOrder simpleOrder = createBasePsOrder()
            .setRequestType(RequestType.REQUEST_TYPE_NEW_ORDER)
            .setQty(quantity)
            .setPrice(price)
            .setOrdType(PsOrderType.PS_ORDER_TYPE_MARKET)
            .build();
        simpleOrderMessage = createMockMessage(simpleOrder);

        // Create COMPLEX ORDER message
        PsOrder complexOrder = createBasePsOrder()
            .setRequestType(RequestType.REQUEST_TYPE_NEW_ORDER)
            .setQty(quantity)
            .setPrice(price)
            .setPremium(premium)
            .setOrdType(PsOrderType.PS_ORDER_TYPE_LIMIT)
            .setTimeInForce(TimeInForce.TIME_IN_FORCE_GOOD_TILL_CANCEL)
            .setExpireTime(System.currentTimeMillis() + 86400000) // 24 hours
            .build();
        complexOrderMessage = createMockMessage(complexOrder);

        // Create LARGE QUANTITY ORDER message
        PsOrder largeQuantityOrder = createBasePsOrder()
            .setRequestType(RequestType.REQUEST_TYPE_NEW_ORDER)
            .setQty(largeQuantity)
            .setPrice(price)
            .build();
        largeQuantityOrderMessage = createMockMessage(largeQuantityOrder);

        // Create additional test data
        createSymbolAndMarketTestData(quantity, price, premium);
        createOrderTypeTestData(quantity, price);
        createEdgeCaseTestData(quantity, price);
    }

    private void createSymbolAndMarketTestData(UDec128 quantity, UDec128 price, UDec128 premium) {
        // BTC order
        PsOrder btcOrder = createBasePsOrder()
            .setRequestType(RequestType.REQUEST_TYPE_NEW_ORDER)
            .setSymbol("BTCUSDT")
            .setQty(quantity)
            .setPrice(price)
            .setMarketCode("SPOT")
            .build();
        btcOrderMessage = createMockMessage(btcOrder);

        // ETH order
        PsOrder ethOrder = createBasePsOrder()
            .setRequestType(RequestType.REQUEST_TYPE_NEW_ORDER)
            .setSymbol("ETHUSDT")
            .setQty(UDec128Util.from(BigDecimal.valueOf(1000.0)))
            .setPrice(UDec128Util.from(BigDecimal.valueOf(3000.0)))
            .setMarketCode("SPOT")
            .build();
        ethOrderMessage = createMockMessage(ethOrder);

        // Futures order
        PsOrder futuresOrder = createBasePsOrder()
            .setRequestType(RequestType.REQUEST_TYPE_NEW_ORDER)
            .setSymbol("BTCUSDT-PERP")
            .setQty(quantity)
            .setPrice(price)
            .setPremium(premium)
            .setMarketCode("FUTURES")
            .setTimeInForce(TimeInForce.TIME_IN_FORCE_GOOD_TILL_CANCEL)
            .build();
        futuresOrderMessage = createMockMessage(futuresOrder);

        // Spot order
        PsOrder spotOrder = createBasePsOrder()
            .setRequestType(RequestType.REQUEST_TYPE_NEW_ORDER)
            .setSymbol("BTCUSDT")
            .setQty(quantity)
            .setPrice(price)
            .setMarketCode("SPOT")
            .setTimeInForce(TimeInForce.TIME_IN_FORCE_DAY)
            .build();
        spotOrderMessage = createMockMessage(spotOrder);
    }

    private void createOrderTypeTestData(UDec128 quantity, UDec128 price) {
        // Buy Limit Order
        PsOrder buyLimitOrder = createBasePsOrder()
            .setRequestType(RequestType.REQUEST_TYPE_NEW_ORDER)
            .setSide(Side.SIDE_BUY)
            .setOrdType(PsOrderType.PS_ORDER_TYPE_LIMIT)
            .setQty(quantity)
            .setPrice(price)
            .setTimeInForce(TimeInForce.TIME_IN_FORCE_DAY)
            .build();
        buyLimitOrderMessage = createMockMessage(buyLimitOrder);

        // Sell Limit Order
        PsOrder sellLimitOrder = createBasePsOrder()
            .setRequestType(RequestType.REQUEST_TYPE_NEW_ORDER)
            .setSide(Side.SIDE_SELL)
            .setOrdType(PsOrderType.PS_ORDER_TYPE_LIMIT)
            .setQty(quantity)
            .setPrice(UDec128Util.from(BigDecimal.valueOf(51000.0)))
            .setTimeInForce(TimeInForce.TIME_IN_FORCE_DAY)
            .build();
        sellLimitOrderMessage = createMockMessage(sellLimitOrder);

        // Buy Market Order
        PsOrder buyMarketOrder = createBasePsOrder()
            .setRequestType(RequestType.REQUEST_TYPE_NEW_ORDER)
            .setSide(Side.SIDE_BUY)
            .setOrdType(PsOrderType.PS_ORDER_TYPE_MARKET)
            .setQty(quantity)
            .setTimeInForce(TimeInForce.TIME_IN_FORCE_IMMEDIATE_OR_CANCEL)
            .build();
        buyMarketOrderMessage = createMockMessage(buyMarketOrder);

        // Sell Market Order
        PsOrder sellMarketOrder = createBasePsOrder()
            .setRequestType(RequestType.REQUEST_TYPE_NEW_ORDER)
            .setSide(Side.SIDE_SELL)
            .setOrdType(PsOrderType.PS_ORDER_TYPE_MARKET)
            .setQty(quantity)
            .setTimeInForce(TimeInForce.TIME_IN_FORCE_IMMEDIATE_OR_CANCEL)
            .build();
        sellMarketOrderMessage = createMockMessage(sellMarketOrder);

        // Stop Loss Order
        PsOrder stopLossOrder = createBasePsOrder()
            .setRequestType(RequestType.REQUEST_TYPE_NEW_ORDER)
            .setSide(Side.SIDE_SELL)
            .setOrdType(PsOrderType.PS_ORDER_TYPE_STOP)
            .setQty(quantity)
            .setPrice(UDec128Util.from(BigDecimal.valueOf(48000.0)))
            .setTimeInForce(TimeInForce.TIME_IN_FORCE_GOOD_TILL_CANCEL)
            .build();
        stopLossOrderMessage = createMockMessage(stopLossOrder);
    }

    private void createEdgeCaseTestData(UDec128 quantity, UDec128 price) {
        // Minimum quantity order
        PsOrder minQuantityOrder = createBasePsOrder()
            .setRequestType(RequestType.REQUEST_TYPE_NEW_ORDER)
            .setQty(UDec128Util.from(BigDecimal.valueOf(0.00000001)))
            .setPrice(price)
            .build();
        minQuantityOrderMessage = createMockMessage(minQuantityOrder);

        // Maximum quantity order
        PsOrder maxQuantityOrder = createBasePsOrder()
            .setRequestType(RequestType.REQUEST_TYPE_NEW_ORDER)
            .setQty(UDec128Util.from(BigDecimal.valueOf(999999999.0)))
            .setPrice(price)
            .build();
        maxQuantityOrderMessage = createMockMessage(maxQuantityOrder);

        // Expired order
        PsOrder expiredOrder = createBasePsOrder()
            .setRequestType(RequestType.REQUEST_TYPE_NEW_ORDER)
            .setQty(quantity)
            .setPrice(price)
            .setExpireTime(System.currentTimeMillis() - 1000) // Already expired
            .setTimeInForce(TimeInForce.TIME_IN_FORCE_GOOD_TILL_CANCEL)
            .build();
        expiredOrderMessage = createMockMessage(expiredOrder);

        // Invalid order (missing required fields)
        PsOrder invalidOrder = PsOrder.newBuilder()
            .setClOrdId("INVALID_ORDER")
            .setRequestType(RequestType.REQUEST_TYPE_NEW_ORDER)
            // Missing symbol, side, quantity, etc.
            .build();
        invalidOrderMessage = createMockMessage(invalidOrder);
    }

    private PsOrder.Builder createBasePsOrder() {
        return PsOrder.newBuilder()
            .setClOrdId("CL_ORD_" + System.nanoTime())
            .setSymbol("BTCUSDT")
            .setSide(Side.SIDE_BUY)
            .setOrdType(PsOrderType.PS_ORDER_TYPE_LIMIT)
            .setServiceAccountId("SERVICE_123")
            .setHoldingAccountId("HOLDING_123")
            .setMarketCode("SPOT")
            .setTimeInForce(TimeInForce.TIME_IN_FORCE_DAY)
            .setUserId("USER_123")
            .setTraceId("TRACE_" + System.nanoTime())
            .setRequestReceivedTime(System.currentTimeMillis())
            .setBaseBalanceAccountId("BASE_123")
            .setQuoteBalanceAccountId("QUOTE_123");
    }

    @SuppressWarnings("unchecked")
    private Message<Order> createMockMessage(PsOrder psOrder) {
        Message<Order> message = mock(Message.class);
        Order order = Order.builder()
            .psOrder(psOrder)
            .startSequence(System.nanoTime())
            .build();
        when(message.body()).thenReturn(order);
        return message;
    }

    // ========== Order Type Benchmarks ==========

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void onOrderNewOrder(Blackhole bh) {
        try {
            orderEvent.onOrder(newOrderMessage);
        } catch (Exception e) {
            bh.consume(e);
        }
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void onOrderCancelOrder(Blackhole bh) {
        try {
            orderEvent.onOrder(cancelOrderMessage);
        } catch (Exception e) {
            bh.consume(e);
        }
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void onOrderEditOrder(Blackhole bh) {
        try {
            orderEvent.onOrder(editOrderMessage);
        } catch (Exception e) {
            bh.consume(e);
        }
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void onOrderUnknownOrder(Blackhole bh) {
        try {
            orderEvent.onOrder(unknownOrderMessage);
        } catch (Exception e) {
            bh.consume(e);
        }
    }

    // ========== Order Complexity Benchmarks ==========

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void onOrderSimpleOrder(Blackhole bh) {
        try {
            orderEvent.onOrder(simpleOrderMessage);
        } catch (Exception e) {
            bh.consume(e);
        }
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void onOrderComplexOrder(Blackhole bh) {
        try {
            orderEvent.onOrder(complexOrderMessage);
        } catch (Exception e) {
            bh.consume(e);
        }
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void onOrderLargeQuantityOrder(Blackhole bh) {
        try {
            orderEvent.onOrder(largeQuantityOrderMessage);
        } catch (Exception e) {
            bh.consume(e);
        }
    }

    // ========== Symbol and Market Benchmarks ==========

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void onOrderBtcOrder(Blackhole bh) {
        try {
            orderEvent.onOrder(btcOrderMessage);
        } catch (Exception e) {
            bh.consume(e);
        }
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void onOrderEthOrder(Blackhole bh) {
        try {
            orderEvent.onOrder(ethOrderMessage);
        } catch (Exception e) {
            bh.consume(e);
        }
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void onOrderFuturesOrder(Blackhole bh) {
        try {
            orderEvent.onOrder(futuresOrderMessage);
        } catch (Exception e) {
            bh.consume(e);
        }
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void onOrderSpotOrder(Blackhole bh) {
        try {
            orderEvent.onOrder(spotOrderMessage);
        } catch (Exception e) {
            bh.consume(e);
        }
    }

    // ========== Order Type and Side Benchmarks ==========

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void onOrderBuyLimitOrder(Blackhole bh) {
        try {
            orderEvent.onOrder(buyLimitOrderMessage);
        } catch (Exception e) {
            bh.consume(e);
        }
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void onOrderSellLimitOrder(Blackhole bh) {
        try {
            orderEvent.onOrder(sellLimitOrderMessage);
        } catch (Exception e) {
            bh.consume(e);
        }
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void onOrderBuyMarketOrder(Blackhole bh) {
        try {
            orderEvent.onOrder(buyMarketOrderMessage);
        } catch (Exception e) {
            bh.consume(e);
        }
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void onOrderSellMarketOrder(Blackhole bh) {
        try {
            orderEvent.onOrder(sellMarketOrderMessage);
        } catch (Exception e) {
            bh.consume(e);
        }
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void onOrderStopLossOrder(Blackhole bh) {
        try {
            orderEvent.onOrder(stopLossOrderMessage);
        } catch (Exception e) {
            bh.consume(e);
        }
    }

    // ========== Edge Case Benchmarks ==========

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void onOrderMinQuantityOrder(Blackhole bh) {
        try {
            orderEvent.onOrder(minQuantityOrderMessage);
        } catch (Exception e) {
            bh.consume(e);
        }
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void onOrderMaxQuantityOrder(Blackhole bh) {
        try {
            orderEvent.onOrder(maxQuantityOrderMessage);
        } catch (Exception e) {
            bh.consume(e);
        }
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void onOrderExpiredOrder(Blackhole bh) {
        try {
            orderEvent.onOrder(expiredOrderMessage);
        } catch (Exception e) {
            bh.consume(e);
        }
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void onOrderInvalidOrder(Blackhole bh) {
        try {
            orderEvent.onOrder(invalidOrderMessage);
        } catch (Exception e) {
            bh.consume(e); // Expected exception
        }
    }

    // ========== Throughput Benchmarks ==========

    @Benchmark
    @BenchmarkMode(Mode.Throughput)
    @OperationsPerInvocation(100)
    public void onOrderThroughputMixed(Blackhole bh) {
        // Test mixed order types for realistic throughput
        for (int i = 0; i < 100; i++) {
            try {
                Message<Order> message;
                switch (i % 4) {
                    case 0: message = newOrderMessage; break;
                    case 1: message = cancelOrderMessage; break;
                    case 2: message = simpleOrderMessage; break;
                    default: message = complexOrderMessage; break;
                }
                orderEvent.onOrder(message);
            } catch (Exception e) {
                bh.consume(e);
            }
        }
    }

    @Benchmark
    @BenchmarkMode(Mode.Throughput)
    @OperationsPerInvocation(1000)
    public void onOrderThroughputNewOrdersOnly(Blackhole bh) {
        // Test throughput for new orders only (most common case)
        for (int i = 0; i < 1000; i++) {
            try {
                orderEvent.onOrder(newOrderMessage);
            } catch (Exception e) {
                bh.consume(e);
            }
        }
    }

    @Benchmark
    @BenchmarkMode(Mode.Throughput)
    @OperationsPerInvocation(500)
    public void onOrderThroughputCancelOrdersOnly(Blackhole bh) {
        // Test throughput for cancel orders only
        for (int i = 0; i < 500; i++) {
            try {
                orderEvent.onOrder(cancelOrderMessage);
            } catch (Exception e) {
                bh.consume(e);
            }
        }
    }

    @Benchmark
    @BenchmarkMode(Mode.Throughput)
    @OperationsPerInvocation(200)
    public void onOrderThroughputMarketOrdersOnly(Blackhole bh) {
        // Test throughput for market orders (fastest execution)
        for (int i = 0; i < 200; i++) {
            try {
                Message<Order> message = (i % 2 == 0) ? buyMarketOrderMessage : sellMarketOrderMessage;
                orderEvent.onOrder(message);
            } catch (Exception e) {
                bh.consume(e);
            }
        }
    }

    @Benchmark
    @BenchmarkMode(Mode.Throughput)
    @OperationsPerInvocation(300)
    public void onOrderThroughputLimitOrdersOnly(Blackhole bh) {
        // Test throughput for limit orders
        for (int i = 0; i < 300; i++) {
            try {
                Message<Order> message = (i % 2 == 0) ? buyLimitOrderMessage : sellLimitOrderMessage;
                orderEvent.onOrder(message);
            } catch (Exception e) {
                bh.consume(e);
            }
        }
    }

    @Benchmark
    @BenchmarkMode(Mode.Throughput)
    @OperationsPerInvocation(400)
    public void onOrderThroughputMultiSymbol(Blackhole bh) {
        // Test throughput with multiple symbols
        for (int i = 0; i < 400; i++) {
            try {
                Message<Order> message;
                switch (i % 3) {
                    case 0: message = btcOrderMessage; break;
                    case 1: message = ethOrderMessage; break;
                    default: message = futuresOrderMessage; break;
                }
                orderEvent.onOrder(message);
            } catch (Exception e) {
                bh.consume(e);
            }
        }
    }

    @Benchmark
    @BenchmarkMode(Mode.Throughput)
    @OperationsPerInvocation(250)
    public void onOrderThroughputRealisticTrading(Blackhole bh) {
        // Realistic trading scenario: 60% limit, 30% market, 10% cancel
        for (int i = 0; i < 250; i++) {
            try {
                Message<Order> message;
                int scenario = i % 10;
                if (scenario < 6) {
                    // 60% limit orders
                    message = (i % 2 == 0) ? buyLimitOrderMessage : sellLimitOrderMessage;
                } else if (scenario < 9) {
                    // 30% market orders
                    message = (i % 2 == 0) ? buyMarketOrderMessage : sellMarketOrderMessage;
                } else {
                    // 10% cancel orders
                    message = cancelOrderMessage;
                }
                orderEvent.onOrder(message);
            } catch (Exception e) {
                bh.consume(e);
            }
        }
    }

    // ========== Error Scenario Benchmarks ==========

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void onOrderWithServiceException(Blackhole bh) {
        // Setup OrderService to throw exception
        doThrow(new RuntimeException("Service error"))
            .when(orderService).placeOrder(any(Order.class));

        try {
            orderEvent.onOrder(newOrderMessage);
        } catch (Exception e) {
            bh.consume(e);
        }

        // Reset mock behavior
        doNothing().when(orderService).placeOrder(any(Order.class));
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void onOrderWithCancelException(Blackhole bh) {
        // Setup OrderService to throw exception for cancel
        doThrow(new RuntimeException("Cancel error"))
            .when(orderService).cancelOrder(any(PsOrder.class));

        try {
            orderEvent.onOrder(cancelOrderMessage);
        } catch (Exception e) {
            bh.consume(e);
        }

        // Reset mock behavior
        doNothing().when(orderService).cancelOrder(any(PsOrder.class));
    }

    // ========== Memory Allocation Benchmarks ==========

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void onOrderMemoryAllocation(Blackhole bh) {
        // Test memory allocation patterns by creating fresh messages
        for (int i = 0; i < 10; i++) {
            PsOrder psOrder = createBasePsOrder()
                .setRequestType(RequestType.REQUEST_TYPE_NEW_ORDER)
                .setQty(UDec128Util.from(BigDecimal.valueOf(100.0 + i)))
                .setPrice(UDec128Util.from(BigDecimal.valueOf(50000.0 + i)))
                .setClOrdId("CL_ORD_" + System.nanoTime() + "_" + i)
                .build();

            Message<Order> message = createMockMessage(psOrder);

            try {
                orderEvent.onOrder(message);
            } catch (Exception e) {
                bh.consume(e);
            }
        }
    }

    // ========== Logging Performance Benchmarks ==========

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void onOrderWithTraceLogging(Blackhole bh) {
        // Test performance impact of trace logging
        // Note: In real scenario, logging level would be controlled by configuration
        try {
            orderEvent.onOrder(newOrderMessage);
        } catch (Exception e) {
            bh.consume(e);
        }
    }

    // ========== Concurrent Access Simulation ==========

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    @Threads(4)
    public void onOrderConcurrentAccess(Blackhole bh) {
        // Simulate concurrent access to onOrder method
        try {
            orderEvent.onOrder(newOrderMessage);
        } catch (Exception e) {
            bh.consume(e);
        }
    }

    @Benchmark
    @BenchmarkMode(Mode.Throughput)
    @Threads(8)
    @OperationsPerInvocation(100)
    public void onOrderHighConcurrency(Blackhole bh) {
        // Test high concurrency scenario
        for (int i = 0; i < 100; i++) {
            try {
                Message<Order> message = (i % 2 == 0) ? newOrderMessage : cancelOrderMessage;
                orderEvent.onOrder(message);
            } catch (Exception e) {
                bh.consume(e);
            }
        }
    }

    // ========== Advanced Performance Benchmarks ==========

    @Benchmark
    @BenchmarkMode(Mode.Throughput)
    @OperationsPerInvocation(1000)
    public void onOrderBurstLoad(Blackhole bh) {
        // Simulate burst load scenario
        for (int i = 0; i < 1000; i++) {
            try {
                Message<Order> message;
                // Simulate burst patterns: 80% new orders, 15% cancels, 5% edits
                int type = i % 20;
                if (type < 16) {
                    message = newOrderMessage;
                } else if (type < 19) {
                    message = cancelOrderMessage;
                } else {
                    message = editOrderMessage;
                }
                orderEvent.onOrder(message);
            } catch (Exception e) {
                bh.consume(e);
            }
        }
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void onOrderLatencySensitive(Blackhole bh) {
        // Test latency-sensitive scenario (single order processing)
        try {
            orderEvent.onOrder(buyMarketOrderMessage); // Market orders are fastest
        } catch (Exception e) {
            bh.consume(e);
        }
    }

    @Benchmark
    @BenchmarkMode(Mode.Throughput)
    @Threads(16)
    @OperationsPerInvocation(50)
    public void onOrderExtremelyHighConcurrency(Blackhole bh) {
        // Test extremely high concurrency
        for (int i = 0; i < 50; i++) {
            try {
                Message<Order> message;
                switch (i % 6) {
                    case 0: message = buyLimitOrderMessage; break;
                    case 1: message = sellLimitOrderMessage; break;
                    case 2: message = buyMarketOrderMessage; break;
                    case 3: message = sellMarketOrderMessage; break;
                    case 4: message = cancelOrderMessage; break;
                    default: message = stopLossOrderMessage; break;
                }
                orderEvent.onOrder(message);
            } catch (Exception e) {
                bh.consume(e);
            }
        }
    }

    @Benchmark
    @BenchmarkMode(Mode.Throughput)
    @OperationsPerInvocation(500)
    public void onOrderVariableLoad(Blackhole bh) {
        // Test variable load with different order complexities
        for (int i = 0; i < 500; i++) {
            try {
                Message<Order> message;
                int complexity = i % 10;
                if (complexity < 3) {
                    message = simpleOrderMessage; // 30% simple
                } else if (complexity < 7) {
                    message = newOrderMessage; // 40% normal
                } else if (complexity < 9) {
                    message = complexOrderMessage; // 20% complex
                } else {
                    message = largeQuantityOrderMessage; // 10% large quantity
                }
                orderEvent.onOrder(message);
            } catch (Exception e) {
                bh.consume(e);
            }
        }
    }

    // ========== Error Handling Performance ==========

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void onOrderErrorRecovery(Blackhole bh) {
        // Test error recovery performance
        try {
            // First, cause an error
            orderEvent.onOrder(invalidOrderMessage);
        } catch (Exception e) {
            bh.consume(e);
        }

        try {
            // Then, process a valid order
            orderEvent.onOrder(newOrderMessage);
        } catch (Exception e) {
            bh.consume(e);
        }
    }

    @Benchmark
    @BenchmarkMode(Mode.Throughput)
    @OperationsPerInvocation(100)
    public void onOrderMixedValidInvalid(Blackhole bh) {
        // Test mixed valid and invalid orders
        for (int i = 0; i < 100; i++) {
            try {
                Message<Order> message = (i % 10 == 0) ? invalidOrderMessage : newOrderMessage;
                orderEvent.onOrder(message);
            } catch (Exception e) {
                bh.consume(e);
            }
        }
    }

    // ========== Memory and GC Impact Benchmarks ==========

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void onOrderMinimalGC(Blackhole bh) {
        // Test with minimal GC impact (reuse objects)
        try {
            orderEvent.onOrder(newOrderMessage); // Reuse same message
        } catch (Exception e) {
            bh.consume(e);
        }
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void onOrderHighGC(Blackhole bh) {
        // Test with high GC impact (create new objects)
        PsOrder psOrder = createBasePsOrder()
            .setRequestType(RequestType.REQUEST_TYPE_NEW_ORDER)
            .setQty(UDec128Util.from(BigDecimal.valueOf(Math.random() * 1000)))
            .setPrice(UDec128Util.from(BigDecimal.valueOf(50000 + Math.random() * 1000)))
            .setClOrdId("FRESH_" + System.nanoTime())
            .build();
        Message<Order> message = createMockMessage(psOrder);

        try {
            orderEvent.onOrder(message);
        } catch (Exception e) {
            bh.consume(e);
        }
    }

    // ========== Warmup and Steady State Benchmarks ==========

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    @Warmup(iterations = 10, time = 1, timeUnit = TimeUnit.SECONDS)
    public void onOrderWellWarmedUp(Blackhole bh) {
        // Test performance after extensive warmup
        try {
            orderEvent.onOrder(newOrderMessage);
        } catch (Exception e) {
            bh.consume(e);
        }
    }

    @Benchmark
    @BenchmarkMode(Mode.Throughput)
    @OperationsPerInvocation(10000)
    public void onOrderSteadyState(Blackhole bh) {
        // Test steady-state performance
        for (int i = 0; i < 10000; i++) {
            try {
                orderEvent.onOrder(newOrderMessage);
            } catch (Exception e) {
                bh.consume(e);
            }
        }
    }
}
