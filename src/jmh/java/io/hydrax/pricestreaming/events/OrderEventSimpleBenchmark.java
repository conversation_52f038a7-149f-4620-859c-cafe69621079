package io.hydrax.pricestreaming.events;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import io.hydrax.aeron.client.ClientManager;
import io.hydrax.pricestreaming.domain.Order;
import io.hydrax.pricestreaming.service.OrderService;
import io.hydrax.pricestreaming.utils.UDec128Util;
import io.hydrax.proto.metwo.match.*;
import io.vertx.core.eventbus.Message;
import java.math.BigDecimal;
import java.util.concurrent.TimeUnit;
import org.openjdk.jmh.annotations.*;
import org.openjdk.jmh.infra.Blackhole;

/**
 * Simple JMH Benchmark for OrderEvent.onOrder method
 * 
 * This is a simplified version that focuses on core functionality
 * without complex mocking to ensure compilation works.
 *
 * Run with:
 * ./gradlew jmh --include=".*OrderEventSimpleBenchmark.*"
 */
@BenchmarkMode({Mode.Throughput})
@OutputTimeUnit(TimeUnit.NANOSECONDS)
@State(Scope.Benchmark)
@Fork(value = 1, jvmArgs = {"-Xms1G", "-Xmx1G", "-XX:+UseG1GC"})
@Warmup(iterations = 2, time = 1, timeUnit = TimeUnit.SECONDS)
@Measurement(iterations = 3, time = 2, timeUnit = TimeUnit.SECONDS)
public class OrderEventSimpleBenchmark {

    private OrderEvent orderEvent;
    private TestMessage newOrderMessage;
    private TestMessage cancelOrderMessage;
    private TestMessage marketOrderMessage;

    // Simple test message implementation
    private static class TestMessage implements Message<Order> {
        private final Order order;

        public TestMessage(Order order) {
            this.order = order;
        }

        @Override
        public Order body() {
            return order;
        }

        @Override
        public String address() {
            return "test";
        }

        @Override
        public io.vertx.core.MultiMap headers() {
            return io.vertx.core.MultiMap.caseInsensitiveMultiMap();
        }

        @Override
        public String replyAddress() {
            return null;
        }

        @Override
        public boolean isSend() {
            return true;
        }

        public void reply(Object message) {
            // No-op for testing
        }

        public <R> void reply(Object message, io.vertx.core.Handler<io.vertx.core.AsyncResult<Message<R>>> replyHandler) {
            // No-op for testing
        }

        public void reply(Object message, io.vertx.core.eventbus.DeliveryOptions options) {
            // No-op for testing
        }

        public <R> void reply(Object message, io.vertx.core.eventbus.DeliveryOptions options, io.vertx.core.Handler<io.vertx.core.AsyncResult<Message<R>>> replyHandler) {
            // No-op for testing
        }

        public void fail(int failureCode, String message) {
            // No-op for testing
        }

        public <R> io.vertx.core.Future<Message<R>> replyAndRequest(Object message, io.vertx.core.eventbus.DeliveryOptions options) {
            // No-op for testing
            return null;
        }

        public <R> io.vertx.core.Future<Message<R>> replyAndRequest(Object message) {
            // No-op for testing
            return null;
        }
    }

    // Simple test implementations using mocks
    private OrderService orderService;
    private ClientManager clientManager;

    @Setup(Level.Trial)
    public void setupTrial() {
        // Create mock implementations
        orderService = mock(OrderService.class);
        clientManager = mock(ClientManager.class);

        // Setup mock behaviors
        doNothing().when(orderService).placeOrder(any(Order.class));
        doNothing().when(orderService).cancelOrder(any(PsOrder.class));
        doNothing().when(clientManager).send(any());
        doNothing().when(clientManager).sendToOwn(any());

        // Create OrderEvent instance
        orderEvent = new OrderEvent(orderService, clientManager);

        // Create test data
        createTestData();
    }

    private void createTestData() {
        // Create NEW ORDER message
        PsOrder newOrder = PsOrder.newBuilder()
            .setClOrdId("NEW_ORDER_" + System.nanoTime())
            .setSymbol("BTCUSDT")
            .setSide(Side.SIDE_BUY)
            .setOrdType(PsOrderType.PS_ORDER_TYPE_LIMIT)
            .setQty(UDec128Util.from(BigDecimal.valueOf(100.0)))
            .setPrice(UDec128Util.from(BigDecimal.valueOf(50000.0)))
            .setRequestType(RequestType.REQUEST_TYPE_NEW_ORDER)
            .setServiceAccountId("SERVICE_123")
            .setHoldingAccountId("HOLDING_123")
            .setMarketCode("SPOT")
            .setTimeInForce(TimeInForce.TIME_IN_FORCE_DAY)
            .setUserId("USER_123")
            .setTraceId("TRACE_" + System.nanoTime())
            .setRequestReceivedTime(System.currentTimeMillis())
            .setBaseBalanceAccountId("BASE_123")
            .setQuoteBalanceAccountId("QUOTE_123")
            .build();

        Order newOrderObj = Order.builder()
            .psOrder(newOrder)
            .startSequence(System.nanoTime())
            .build();
        newOrderMessage = new TestMessage(newOrderObj);

        // Create CANCEL ORDER message
        PsOrder cancelOrder = PsOrder.newBuilder()
            .setClOrdId("CANCEL_ORDER_" + System.nanoTime())
            .setSymbol("BTCUSDT")
            .setSide(Side.SIDE_BUY)
            .setOrdType(PsOrderType.PS_ORDER_TYPE_LIMIT)
            .setRequestType(RequestType.REQUEST_TYPE_CANCEL_ORDER)
            .setOrderId("ORDER_123")
            .setOrigClOrdId("ORIG_123")
            .setServiceAccountId("SERVICE_123")
            .setUserId("USER_123")
            .build();

        Order cancelOrderObj = Order.builder()
            .psOrder(cancelOrder)
            .startSequence(System.nanoTime())
            .build();
        cancelOrderMessage = new TestMessage(cancelOrderObj);

        // Create MARKET ORDER message
        PsOrder marketOrder = PsOrder.newBuilder()
            .setClOrdId("MARKET_ORDER_" + System.nanoTime())
            .setSymbol("BTCUSDT")
            .setSide(Side.SIDE_BUY)
            .setOrdType(PsOrderType.PS_ORDER_TYPE_MARKET)
            .setQty(UDec128Util.from(BigDecimal.valueOf(100.0)))
            .setRequestType(RequestType.REQUEST_TYPE_NEW_ORDER)
            .setServiceAccountId("SERVICE_123")
            .setHoldingAccountId("HOLDING_123")
            .setMarketCode("SPOT")
            .setTimeInForce(TimeInForce.TIME_IN_FORCE_IMMEDIATE_OR_CANCEL)
            .setUserId("USER_123")
            .setTraceId("TRACE_" + System.nanoTime())
            .setRequestReceivedTime(System.currentTimeMillis())
            .setBaseBalanceAccountId("BASE_123")
            .setQuoteBalanceAccountId("QUOTE_123")
            .build();

        Order marketOrderObj = Order.builder()
            .psOrder(marketOrder)
            .startSequence(System.nanoTime())
            .build();
        marketOrderMessage = new TestMessage(marketOrderObj);
    }

    // ========== Basic Benchmarks ==========

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void onOrderNewOrder(Blackhole bh) {
        try {
            orderEvent.onOrder(newOrderMessage);
        } catch (Exception e) {
            bh.consume(e);
        }
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void onOrderCancelOrder(Blackhole bh) {
        try {
            orderEvent.onOrder(cancelOrderMessage);
        } catch (Exception e) {
            bh.consume(e);
        }
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void onOrderMarketOrder(Blackhole bh) {
        try {
            orderEvent.onOrder(marketOrderMessage);
        } catch (Exception e) {
            bh.consume(e);
        }
    }

    // ========== Throughput Benchmarks ==========

    @Benchmark
    @BenchmarkMode(Mode.Throughput)
    @OperationsPerInvocation(100)
    public void onOrderThroughputMixed(Blackhole bh) {
        for (int i = 0; i < 100; i++) {
            try {
                Message<Order> message;
                switch (i % 3) {
                    case 0: message = newOrderMessage; break;
                    case 1: message = cancelOrderMessage; break;
                    default: message = marketOrderMessage; break;
                }
                orderEvent.onOrder(message);
            } catch (Exception e) {
                bh.consume(e);
            }
        }
    }

    @Benchmark
    @BenchmarkMode(Mode.Throughput)
    @OperationsPerInvocation(500)
    public void onOrderThroughputNewOrdersOnly(Blackhole bh) {
        for (int i = 0; i < 500; i++) {
            try {
                orderEvent.onOrder(newOrderMessage);
            } catch (Exception e) {
                bh.consume(e);
            }
        }
    }

    // ========== Concurrent Benchmarks ==========

    @Benchmark
    @BenchmarkMode(Mode.Throughput)
    @Threads(4)
    @OperationsPerInvocation(50)
    public void onOrderConcurrent(Blackhole bh) {
        for (int i = 0; i < 50; i++) {
            try {
                Message<Order> message = (i % 2 == 0) ? newOrderMessage : marketOrderMessage;
                orderEvent.onOrder(message);
            } catch (Exception e) {
                bh.consume(e);
            }
        }
    }
}
