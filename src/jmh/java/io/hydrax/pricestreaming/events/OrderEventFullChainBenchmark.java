package io.hydrax.pricestreaming.events;

import io.aeron.ExclusivePublication;
import io.aeron.cluster.client.AeronCluster;
import io.hydrax.aeron.client.ClientManager;
import io.hydrax.pricestreaming.domain.Order;
import io.hydrax.pricestreaming.service.OrderService;
import io.hydrax.pricestreaming.utils.UDec128Util;
import io.hydrax.proto.metwo.match.*;
import io.vertx.core.eventbus.Message;
import org.openjdk.jmh.annotations.*;
import org.openjdk.jmh.infra.Blackhole;
import org.mockito.Mockito;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 完整链路的 JMH 基准测试，包括 mock aeronCluster.offer 和 publication.offer
 * 
 * 这个测试专门用于测试从 OrderEvent.onOrder 到 ClientManager 内部方法的完整调用链路
 *
 * Run with:
 * ./gradlew jmh --include=".*OrderEventFullChainBenchmark.*"
 */
@BenchmarkMode({Mode.AverageTime, Mode.Throughput})
@OutputTimeUnit(TimeUnit.NANOSECONDS)
@State(Scope.Benchmark)
@Fork(value = 1, jvmArgs = {"-Xms2G", "-Xmx2G", "-XX:+UseG1GC"})
@Warmup(iterations = 3, time = 2, timeUnit = TimeUnit.SECONDS)
@Measurement(iterations = 5, time = 3, timeUnit = TimeUnit.SECONDS)
public class OrderEventFullChainBenchmark {

    private OrderEvent orderEvent;
    private OrderService orderService;
    private ClientManager clientManager;
    
    // Mock Aeron 组件
    private AeronCluster mockAeronCluster;
    private ExclusivePublication mockPublication;
    
    // 测试数据
    private Message<Order> newOrderMessage;
    private Message<Order> cancelOrderMessage;
    private Message<Order> marketOrderMessage;
    
    // 性能计数器
    private AtomicLong offerCallCount = new AtomicLong(0);
    private AtomicLong publicationCallCount = new AtomicLong(0);
    private AtomicLong totalLatency = new AtomicLong(0);

    @Setup(Level.Trial)
    public void setupTrial() {
        setupMockAeronComponents();
        setupOrderService();
        setupClientManager();
        
        orderEvent = new OrderEvent(orderService, clientManager);
        createTestData();
    }

    /**
     * 设置 Mock Aeron 组件
     */
    private void setupMockAeronComponents() {
        // Mock AeronCluster
        mockAeronCluster = mock(AeronCluster.class);
        
        // Mock aeronCluster.offer 方法
        when(mockAeronCluster.offer(any(), anyInt(), anyInt())).thenAnswer(invocation -> {
            long startTime = System.nanoTime();
            offerCallCount.incrementAndGet();
            
            // 模拟 Aeron cluster offer 的处理时间
            simulateAeronClusterProcessing();
            
            long endTime = System.nanoTime();
            totalLatency.addAndGet(endTime - startTime);
            
            // 返回成功的 position
            return startTime; // 使用 startTime 作为 position
        });
        
        // Mock ExclusivePublication
        mockPublication = mock(ExclusivePublication.class);
        
        // Mock publication.offer 方法
        when(mockPublication.offer(any(), anyInt(), anyInt())).thenAnswer(invocation -> {
            long startTime = System.nanoTime();
            publicationCallCount.incrementAndGet();
            
            // 模拟 publication offer 的处理时间
            simulatePublicationProcessing();
            
            long endTime = System.nanoTime();
            totalLatency.addAndGet(endTime - startTime);
            
            // 返回成功的 position
            return startTime; // 使用 startTime 作为 position
        });
    }

    /**
     * 设置 OrderService mock
     */
    private void setupOrderService() {
        orderService = mock(OrderService.class);
        
        // Mock placeOrder 方法，模拟调用 clientManager.send
        doAnswer(invocation -> {
            Order order = invocation.getArgument(0);
            // 模拟 OrderService 内部调用 clientManager.send
            simulateOrderServiceProcessing(order);
            return null;
        }).when(orderService).placeOrder(any(Order.class));
        
        // Mock cancelOrder 方法
        doAnswer(invocation -> {
            PsOrder order = invocation.getArgument(0);
            // 模拟 OrderService 内部调用 clientManager.send
            simulateCancelOrderProcessing(order);
            return null;
        }).when(orderService).cancelOrder(any(PsOrder.class));
    }

    /**
     * 设置 ClientManager mock，包括内部的 aeronCluster 和 publication
     */
    private void setupClientManager() {
        clientManager = mock(ClientManager.class);
        
        // Mock send 方法，模拟内部调用 aeronCluster.offer
        doAnswer(invocation -> {
            Object message = invocation.getArgument(0);
            return simulateClientManagerSend(message);
        }).when(clientManager).send(any());
        
        // Mock sendToOwn 方法，模拟内部调用 publication.offer
        doAnswer(invocation -> {
            Object message = invocation.getArgument(0);
            return simulateClientManagerSendToOwn(message);
        }).when(clientManager).sendToOwn(any());
        
        // 尝试注入 mock 的 aeronCluster 和 publication（如果可能的话）
        try {
            injectMockFields();
        } catch (Exception e) {
            // 如果注入失败，继续使用 mock 方法
        }
    }

    /**
     * 尝试通过反射注入 mock 字段
     */
    private void injectMockFields() throws Exception {
        // 这里可以尝试通过反射注入 mock 的 aeronCluster 和 publication
        // 但由于 ClientManager 是外部依赖，我们主要通过 mock 方法来模拟
    }

    /**
     * 模拟 ClientManager.send 内部调用 aeronCluster.offer
     */
    private Object simulateClientManagerSend(Object message) {
        // 模拟消息序列化
        byte[] serializedMessage = simulateMessageSerialization(message);
        
        // 调用 mock 的 aeronCluster.offer
        long position = mockAeronCluster.offer(serializedMessage, 0, serializedMessage.length);
        
        return position;
    }

    /**
     * 模拟 ClientManager.sendToOwn 内部调用 publication.offer
     */
    private Object simulateClientManagerSendToOwn(Object message) {
        // 模拟消息序列化
        byte[] serializedMessage = simulateMessageSerialization(message);
        
        // 调用 mock 的 publication.offer
        long position = mockPublication.offer(serializedMessage, 0, serializedMessage.length);
        
        return position;
    }

    /**
     * 模拟 OrderService 处理逻辑
     */
    private void simulateOrderServiceProcessing(Order order) {
        // 模拟订单验证和处理
        simulateOrderValidation();
        
        // 模拟调用 clientManager.send（发送执行报告）
        clientManager.send(createMockExecutionReport(order));
    }

    /**
     * 模拟取消订单处理逻辑
     */
    private void simulateCancelOrderProcessing(PsOrder order) {
        // 模拟取消订单验证和处理
        simulateOrderValidation();
        
        // 模拟调用 clientManager.send（发送取消确认）
        clientManager.send(createMockCancelConfirmation(order));
    }

    /**
     * 模拟消息序列化
     */
    private byte[] simulateMessageSerialization(Object message) {
        // 模拟 protobuf 序列化的开销
        String messageStr = message.toString();
        byte[] bytes = messageStr.getBytes();
        
        // 模拟序列化处理时间
        simulateSerializationLatency(bytes.length);
        
        return bytes;
    }

    /**
     * 模拟 Aeron Cluster 处理时间
     */
    private void simulateAeronClusterProcessing() {
        // 模拟 2-10 微秒的集群处理时间
        busyWait((long)(Math.random() * 8000 + 2000)); // 2-10 微秒
    }

    /**
     * 模拟 Publication 处理时间
     */
    private void simulatePublicationProcessing() {
        // 模拟 0.5-3 微秒的发布处理时间
        busyWait((long)(Math.random() * 2500 + 500)); // 0.5-3 微秒
    }

    /**
     * 模拟订单验证时间
     */
    private void simulateOrderValidation() {
        // 模拟 1-5 微秒的验证时间
        busyWait((long)(Math.random() * 4000 + 1000)); // 1-5 微秒
    }

    /**
     * 模拟序列化延迟
     */
    private void simulateSerializationLatency(int messageSize) {
        // 根据消息大小模拟序列化时间
        long latency = messageSize / 100 + 500; // 基础 500ns + 每100字节1ns
        busyWait(latency);
    }

    /**
     * 忙等待指定的纳秒数
     */
    private void busyWait(long nanos) {
        long start = System.nanoTime();
        while (System.nanoTime() - start < nanos) {
            // 忙等待
        }
    }

    /**
     * 创建模拟的执行报告
     */
    private Object createMockExecutionReport(Order order) {
        return "ExecutionReport{orderId=" + order.getPsOrder().getClOrdId() + "}";
    }

    /**
     * 创建模拟的取消确认
     */
    private Object createMockCancelConfirmation(PsOrder order) {
        return "CancelConfirmation{orderId=" + order.getClOrdId() + "}";
    }

    /**
     * 创建测试数据
     */
    private void createTestData() {
        // 创建新订单消息
        PsOrder newOrder = PsOrder.newBuilder()
            .setClOrdId("NEW_ORDER_" + System.nanoTime())
            .setSymbol("BTCUSDT")
            .setSide(Side.SIDE_BUY)
            .setOrdType(PsOrderType.PS_ORDER_TYPE_LIMIT)
            .setQty(UDec128Util.from(BigDecimal.valueOf(100.0)))
            .setPrice(UDec128Util.from(BigDecimal.valueOf(50000.0)))
            .setRequestType(RequestType.REQUEST_TYPE_NEW_ORDER)
            .setServiceAccountId("SERVICE_123")
            .setUserId("USER_123")
            .build();
        newOrderMessage = createMockMessage(newOrder);

        // 创建取消订单消息
        PsOrder cancelOrder = PsOrder.newBuilder()
            .setClOrdId("CANCEL_ORDER_" + System.nanoTime())
            .setSymbol("BTCUSDT")
            .setRequestType(RequestType.REQUEST_TYPE_CANCEL_ORDER)
            .setOrderId("ORDER_123")
            .setUserId("USER_123")
            .build();
        cancelOrderMessage = createMockMessage(cancelOrder);

        // 创建市场订单消息
        PsOrder marketOrder = PsOrder.newBuilder()
            .setClOrdId("MARKET_ORDER_" + System.nanoTime())
            .setSymbol("BTCUSDT")
            .setSide(Side.SIDE_BUY)
            .setOrdType(PsOrderType.PS_ORDER_TYPE_MARKET)
            .setQty(UDec128Util.from(BigDecimal.valueOf(100.0)))
            .setRequestType(RequestType.REQUEST_TYPE_NEW_ORDER)
            .setServiceAccountId("SERVICE_123")
            .setUserId("USER_123")
            .build();
        marketOrderMessage = createMockMessage(marketOrder);
    }

    @SuppressWarnings("unchecked")
    private Message<Order> createMockMessage(PsOrder psOrder) {
        Message<Order> message = mock(Message.class);
        Order order = Order.builder()
            .psOrder(psOrder)
            .startSequence(System.nanoTime())
            .build();
        when(message.body()).thenReturn(order);
        return message;
    }

    // ========== 完整链路基准测试 ==========

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void onOrderNewOrderFullChain(Blackhole bh) {
        try {
            orderEvent.onOrder(newOrderMessage);
            bh.consume(offerCallCount.get());
        } catch (Exception e) {
            bh.consume(e);
        }
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void onOrderCancelOrderFullChain(Blackhole bh) {
        try {
            orderEvent.onOrder(cancelOrderMessage);
            bh.consume(offerCallCount.get());
        } catch (Exception e) {
            bh.consume(e);
        }
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void onOrderMarketOrderFullChain(Blackhole bh) {
        try {
            orderEvent.onOrder(marketOrderMessage);
            bh.consume(offerCallCount.get());
        } catch (Exception e) {
            bh.consume(e);
        }
    }

    // ========== Aeron 组件性能测试 ==========

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void aeronClusterOfferOnly(Blackhole bh) {
        byte[] testMessage = "test message".getBytes();
        long position = mockAeronCluster.offer(testMessage, 0, testMessage.length);
        bh.consume(position);
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void publicationOfferOnly(Blackhole bh) {
        byte[] testMessage = "test message".getBytes();
        long position = mockPublication.offer(testMessage, 0, testMessage.length);
        bh.consume(position);
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void clientManagerSendOnly(Blackhole bh) {
        Object testMessage = "test execution report";
        Object result = simulateClientManagerSend(testMessage);
        bh.consume(result);
    }

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void clientManagerSendToOwnOnly(Blackhole bh) {
        Object testMessage = "test local message";
        Object result = simulateClientManagerSendToOwn(testMessage);
        bh.consume(result);
    }

    // ========== 吞吐量测试 ==========

    @Benchmark
    @BenchmarkMode(Mode.Throughput)
    @OperationsPerInvocation(100)
    public void onOrderFullChainThroughput(Blackhole bh) {
        for (int i = 0; i < 100; i++) {
            try {
                Message<Order> message;
                switch (i % 3) {
                    case 0: message = newOrderMessage; break;
                    case 1: message = cancelOrderMessage; break;
                    default: message = marketOrderMessage; break;
                }
                orderEvent.onOrder(message);
            } catch (Exception e) {
                bh.consume(e);
            }
        }
        bh.consume(offerCallCount.get());
        bh.consume(publicationCallCount.get());
    }

    @Benchmark
    @BenchmarkMode(Mode.Throughput)
    @OperationsPerInvocation(500)
    public void aeronClusterOfferThroughput(Blackhole bh) {
        byte[] testMessage = "throughput test message".getBytes();
        for (int i = 0; i < 500; i++) {
            long position = mockAeronCluster.offer(testMessage, 0, testMessage.length);
            bh.consume(position);
        }
    }

    @Benchmark
    @BenchmarkMode(Mode.Throughput)
    @OperationsPerInvocation(1000)
    public void publicationOfferThroughput(Blackhole bh) {
        byte[] testMessage = "throughput test message".getBytes();
        for (int i = 0; i < 1000; i++) {
            long position = mockPublication.offer(testMessage, 0, testMessage.length);
            bh.consume(position);
        }
    }

    // ========== 并发测试 ==========

    @Benchmark
    @BenchmarkMode(Mode.Throughput)
    @Threads(4)
    @OperationsPerInvocation(50)
    public void onOrderFullChainConcurrent(Blackhole bh) {
        for (int i = 0; i < 50; i++) {
            try {
                Message<Order> message = (i % 2 == 0) ? newOrderMessage : marketOrderMessage;
                orderEvent.onOrder(message);
            } catch (Exception e) {
                bh.consume(e);
            }
        }
        bh.consume(offerCallCount.get());
    }

    @Benchmark
    @BenchmarkMode(Mode.Throughput)
    @Threads(8)
    @OperationsPerInvocation(25)
    public void aeronClusterOfferConcurrent(Blackhole bh) {
        byte[] testMessage = "concurrent test message".getBytes();
        for (int i = 0; i < 25; i++) {
            long position = mockAeronCluster.offer(testMessage, 0, testMessage.length);
            bh.consume(position);
        }
    }

    // ========== 性能分析测试 ==========

    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    public void onOrderWithMetrics(Blackhole bh) {
        long startTime = System.nanoTime();

        try {
            orderEvent.onOrder(newOrderMessage);
        } catch (Exception e) {
            bh.consume(e);
        }

        long endTime = System.nanoTime();
        long totalTime = endTime - startTime;

        bh.consume(totalTime);
        bh.consume(offerCallCount.get());
        bh.consume(publicationCallCount.get());
        bh.consume(totalLatency.get());
    }

    // ========== 清理方法 ==========

    @TearDown(Level.Iteration)
    public void resetCounters() {
        offerCallCount.set(0);
        publicationCallCount.set(0);
        totalLatency.set(0);
    }
}
